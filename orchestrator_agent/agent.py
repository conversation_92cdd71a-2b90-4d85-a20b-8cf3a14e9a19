# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Orchestrator Agent - Intelligent Question Routing
Routes user questions to appropriate specialized agents using LLM-based decision making
"""

import logging
import sys
import os
import importlib.util
from pathlib import Path
from google.adk.agents import Agent

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Set environment variables
os.environ.setdefault('DATABASE_URL', 'postgresql+asyncpg://postgres:<EMAIL>:5432/postgres')

class QuestionRouter:
    """Intelligent question router using LLM-based classification"""
    
    def __init__(self):
        self.classifier_agent = Agent(
            name="question_classifier",
            description="Classifies questions to determine appropriate agent routing",
            model=LiteLlm("gemini-2.0-flash"),
            instruction="""
            You are a question classifier that determines which specialized agent should handle a user's question.

            AVAILABLE AGENTS:
            1. **Q&A Agent**: Handles questions about Dropi company, services, academy, tutorials, and general company information
            2. **Orders Agent**: Handles questions about orders, sales, order management, statistics, order status, and commercial operations

            CLASSIFICATION RULES:
            - Analyze the intent and content of the question
            - Consider what type of information the user is seeking
            - Determine which agent has the appropriate knowledge/tools to answer

            RESPONSE FORMAT:
            Respond with EXACTLY one of these two options:
            - "QA_AGENT" (for Dropi knowledge questions)
            - "ORDERS_AGENT" (for order management questions)

            Do not provide explanations, just the classification.

            EXAMPLES:
            - "¿Qué es Dropi?" → QA_AGENT
            - "¿Cuántas órdenes hay?" → ORDERS_AGENT
            - "¿Cómo funciona Dropi Academy?" → QA_AGENT
            - "¿Qué órdenes están pendientes?" → ORDERS_AGENT
            - "¿Cuáles son los servicios de Dropi?" → QA_AGENT
            - "¿Cuáles son las estadísticas de ventas?" → ORDERS_AGENT
            """
        )
        
        # Import specialized agents
        self.qa_agent = None
        self.orders_agent = None
        self._load_specialized_agents()
    
    def _load_specialized_agents(self):
        """Load the specialized Q&A and Orders agents"""
        # Load Orders Agent first (it's working)
        try:
            orders_path = Path(__file__).parent.parent / "orders_agent_web"
            if str(orders_path) not in sys.path:
                sys.path.insert(0, str(orders_path))

            # Import with a unique name to avoid conflicts
            spec = importlib.util.spec_from_file_location("orders_agent_module", orders_path / "agent.py")
            orders_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(orders_module)

            self.orders_agent = orders_module.root_agent
            logger.info("✅ Orders Agent loaded successfully")

        except Exception as e:
            logger.error(f"❌ Failed to load Orders Agent: {e}")

        # Load Q&A Agent with fallback
        try:
            qa_path = Path(__file__).parent.parent / "qa_agent" / "src"
            if str(qa_path) not in sys.path:
                sys.path.insert(0, str(qa_path))

            # Import with a unique name to avoid conflicts
            spec = importlib.util.spec_from_file_location("qa_agent_module", qa_path / "agent.py")
            qa_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(qa_module)

            self.qa_agent = qa_module.root_agent
            logger.info("✅ Q&A Agent loaded successfully")

        except Exception as e:
            logger.error(f"❌ Failed to load Q&A Agent: {e}")
            # Create a fallback Q&A agent for testing
            self.qa_agent = self._create_fallback_qa_agent()

    def _create_fallback_qa_agent(self):
        """Create a fallback Q&A agent when the main one can't be loaded"""
        # Create a simple Q&A agent with basic Dropi knowledge
        fallback_agent = Agent(
            name="fallback_qa_agent",
            description="Agente básico de Q&A sobre Dropi",
            model=LiteLlm("gemini-2.0-flash"),
            instruction="""
            Eres un asistente especializado en información básica sobre Dropi.

            INFORMACIÓN BÁSICA SOBRE DROPI:
            - Dropi es una plataforma de comercio electrónico y gestión empresarial
            - Ofrece servicios de gestión de órdenes, inventario y ventas
            - Tiene una academia (Dropi Academy) para capacitación
            - Proporciona herramientas para emprendedores y empresas

            Si te preguntan sobre Dropi, proporciona información general basada en lo que sabes.
            Si no tienes información específica, indica que necesitas más detalles o que consulten la documentación oficial.

            Responde siempre en español de manera amigable y profesional.
            """
        )

        logger.info("✅ Fallback Q&A Agent created with basic Dropi knowledge")
        return fallback_agent
    
    async def classify_question(self, question: str) -> str:
        """Classify the question to determine appropriate agent"""
        try:
            # Use keyword-based classification for now (more reliable)
            question_lower = question.lower()

            # Check for order-related keywords
            order_keywords = ['orden', 'pedido', 'venta', 'estadística', 'pendiente', 'completado', 'cuántas órdenes', 'órdenes hay', 'estadísticas de órdenes']
            if any(keyword in question_lower for keyword in order_keywords):
                logger.info(f"🎯 Classified as ORDERS_AGENT (keyword match)")
                return "ORDERS_AGENT"

            # Check for Dropi-related keywords
            dropi_keywords = ['dropi', 'academy', 'servicios', 'qué es', 'cómo funciona', 'empresa', 'compañía']
            if any(keyword in question_lower for keyword in dropi_keywords):
                logger.info(f"🎯 Classified as QA_AGENT (keyword match)")
                return "QA_AGENT"

            # Default to orders agent for ambiguous questions
            logger.info(f"🎯 Classified as ORDERS_AGENT (default)")
            return "ORDERS_AGENT"

        except Exception as e:
            logger.error(f"Error in question classification: {e}")
            # Default to orders agent on error (since it's working)
            return "ORDERS_AGENT"
    
    async def route_question(self, question: str) -> str:
        """Route question to appropriate agent and return response"""
        try:
            # Classify the question
            agent_type = await self.classify_question(question)
            logger.info(f"🎯 Question classified as: {agent_type}")

            # Route to appropriate agent
            if agent_type == "QA_AGENT" and self.qa_agent:
                logger.info("📚 Routing to Q&A Agent")
                response_parts = []
                async for chunk in self.qa_agent.run_async(question):
                    # Handle different types of response chunks
                    if hasattr(chunk, 'text'):
                        response_parts.append(chunk.text)
                    elif hasattr(chunk, 'content'):
                        response_parts.append(chunk.content)
                    elif hasattr(chunk, '__str__'):
                        response_parts.append(str(chunk))
                    else:
                        response_parts.append(str(chunk))
                response = ''.join(response_parts)
                return f"[Q&A Agent] {response}"

            elif agent_type == "ORDERS_AGENT" and self.orders_agent:
                logger.info("🛒 Routing to Orders Agent")
                response_parts = []
                async for chunk in self.orders_agent.run_async(question):
                    # Handle different types of response chunks
                    if hasattr(chunk, 'text'):
                        response_parts.append(chunk.text)
                    elif hasattr(chunk, 'content'):
                        response_parts.append(chunk.content)
                    elif hasattr(chunk, '__str__'):
                        response_parts.append(str(chunk))
                    else:
                        response_parts.append(str(chunk))
                response = ''.join(response_parts)
                return f"[Orders Agent] {response}"

            else:
                return f"❌ Error: No suitable agent available for {agent_type}"

        except Exception as e:
            logger.error(f"Error in question routing: {e}")
            import traceback
            logger.error(f"Full traceback: {traceback.format_exc()}")
            return f"❌ Error processing question: {str(e)}"

# Initialize the router
question_router = QuestionRouter()

# Create a custom orchestrator agent class
class OrchestratorAgent:
    """Intelligent orchestrator that routes questions to specialized agents"""

    def __init__(self):
        self.name = "orchestrator_agent"
        self.description = "Intelligent orchestrator that routes questions to specialized agents"
        self.question_router = question_router

    async def run_async(self, query: str):
        """Route query to appropriate agent and return response"""
        try:
            logger.info(f"🎭 Orchestrator received query: {query}")
            response = await self.question_router.route_question(query)
            logger.info(f"✅ Orchestrator response ready")
            yield response
        except Exception as e:
            logger.error(f"❌ Orchestrator error: {e}")
            yield f"Error: {str(e)}"

# Create the orchestrator instance
root_agent = OrchestratorAgent()

logger.info("🎭 Orchestrator Agent initialized successfully")
